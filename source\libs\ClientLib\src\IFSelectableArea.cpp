#include "IFSelectableArea.h"

void CIFSelectableArea::sub_64CE30(std::n_string a, std::n_string b, std::n_string c) {
    reinterpret_cast<void (__thiscall *)(CIFSelectableArea *, std::n_string, std::n_string, std::n_string)>(0x0064CE30)(
            this, a, b, c);
}

void CIFSelectableArea::sub_64CC30(char a2) {
    reinterpret_cast<void (__thiscall *)(CIFSelectableArea *, char)>(0x0064CC30)(this, a2);
}
