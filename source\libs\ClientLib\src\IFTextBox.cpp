#include "IFTextBox.h"

void CIFTextBox::SetIndentAfterBreak(bool indentAfterBreak) {
    m_identAfterBreak = indentAfterBreak;
}

void CIFTextBox::sub_64F8A0(std::n_wstring &str, int a3, int a4, int a5, int a6, int a7, int a8) {
    reinterpret_cast<void(__thiscall*)(CIFTextBox*, std::n_wstring*, int, int, int, int, int, int)>(0x64F8A0)(this, &str, a3, a4, a5, a6, a7, a8);
}

void CIFTextBox::sub_64F660(std::n_string &str, int a3, int a4, int a5, int a6, int a7, int a8) {
    reinterpret_cast<void(__thiscall*)(CIFTextBox*, std::n_string*, int, int, int, int, int, int)>(0x64F660)(this, &str, a3, a4, a5, a6, a7, a8);
}
